using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace TempConsoleApp1
{
    /// <summary>
    /// 单元格值结果
    /// </summary>
    /// <param name="ValueType">实际值的 C# 类型</param>
    /// <param name="Value">单元格值</param>
    /// <param name="OriginalCellType">NPOI 原始单元格类型</param>
    public sealed record CellValueResult(Type? ValueType, object? Value, CellType OriginalCellType);

    /// <summary>
    /// 工作簿缓存信息
    /// </summary>
    /// <param name="Is1904DateSystem">是否使用1904日期系统</param>
    public sealed record WorkbookCacheInfo(bool Is1904DateSystem);

    /// <summary>
    /// 日期时间转换配置选项
    /// </summary>
    public sealed record DateTimeConversionOptions
    {
        /// <summary>
        /// TimeOnly → DateTime 时使用的默认日期
        /// </summary>
        public DateOnly DefaultDate { get; init; } = DateOnly.FromDateTime(DateTime.Today);

        /// <summary>
        /// DateOnly → DateTime 时使用的默认时间
        /// </summary>
        public TimeOnly DefaultTime { get; init; } = TimeOnly.MinValue;
    }

    public static class ExcelHelper
    {
        // 私有静态字段，通过属性访问以提供更好的封装
        private static WorkbookCacheInfo _workbookCacheInfo = new(false);

        /// <summary>
        /// 获取当前工作簿缓存信息
        /// </summary>
        public static WorkbookCacheInfo WorkbookCacheInfo => _workbookCacheInfo;

        // 常用类型的直接引用（保留用于非映射场景）
        private static readonly Type StringType = typeof(string);
        private static readonly Type BooleanType = typeof(bool);
        private static readonly Type IntType = typeof(int);
        private static readonly Type DoubleType = typeof(double);
        private static readonly Type DateTimeType = typeof(DateTime);
        private static readonly Type DateOnlyType = typeof(DateOnly);
        private static readonly Type TimeOnlyType = typeof(TimeOnly);

        // 日期时间类型缓存，提高性能
        private static readonly HashSet<Type> DateTimeTypes =
        [
            typeof(DateTime), typeof(DateOnly), typeof(TimeOnly),
            typeof(DateTime?), typeof(DateOnly?), typeof(TimeOnly?)
        ];

        // 布尔类型缓存
        private static readonly HashSet<Type> BooleanTypes =
        [
            typeof(bool), typeof(bool?)
        ];
        /// <summary>
        /// NPOI通过单元格类型获取单元格值
        /// </summary>
        /// <param name="cell">NPOI单元格对象</param>
        /// <param name="getFormulaValue">是否获取公式计算结果值，false则返回公式字符串</param>
        /// <returns>包含实际值类型、值和原始单元格类型的结果对象</returns>
        public static CellValueResult NPOIGetValueByCellType(ICell cell, bool getFormulaValue = true)
        {
            if (cell == null)
            {
                return new CellValueResult(null, null, CellType.Blank);
            }

            var originalCellType = cell.CellType;

            // 优化：直接处理最常见的类型，避免 switch 表达式的开销
            object? value;
            Type? valueType;

            switch (originalCellType)
            {
                case CellType.String:
                    value = cell.StringCellValue;
                    valueType = StringType;
                    break;

                case CellType.Boolean:
                    value = cell.BooleanCellValue;
                    valueType = BooleanType;
                    break;

                case CellType.Numeric:
                    if (TryGetTemporalValue(cell, out var temporalValue, out var temporalType))
                    {
                        value = temporalValue;
                        valueType = temporalType;
                    }
                    else
                    {
                        value = cell.NumericCellValue;
                        valueType = DoubleType;
                    }
                    break;

                case CellType.Error:
                    value = GetErrorValue(cell);
                    valueType = StringType;
                    break;

                case CellType.Formula:
                    if (getFormulaValue)
                    {
                        var formulaResult = GetValueByCellFormulaResultType(cell);
                        value = formulaResult.Value;
                        valueType = formulaResult.ValueType;
                    }
                    else
                    {
                        value = cell.CellFormula;
                        valueType = StringType;
                    }
                    break;

                default: // CellType.Unknown, CellType.Blank
                    value = null;
                    valueType = null;
                    break;
            }

            return new CellValueResult(valueType, value, originalCellType);
        }

        /// <summary>
        /// 获取错误单元格的错误信息
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>错误信息字符串</returns>
        private static string GetErrorValue(ICell cell)
        {
            if (cell is XSSFCell xSSFCell)
            {
                return xSSFCell.ErrorCellString;
            }
            else
            {
                byte errorCode = cell.ErrorCellValue;
                return FormulaError.ForInt(errorCode).String;
            }
        }

        /// <summary>
        /// NPOI公式单元格获取值
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>包含实际值类型、值和原始单元格类型的结果对象</returns>
        private static CellValueResult GetValueByCellFormulaResultType(ICell cell)
        {
            if (cell == null)
            {
                return new CellValueResult(null, null, CellType.Blank);
            }

            var cachedResultType = cell.CachedFormulaResultType;
            object? value;
            Type? valueType;

            switch (cachedResultType)
            {
                case CellType.String:
                    value = cell.StringCellValue;
                    valueType = StringType;
                    break;

                case CellType.Boolean:
                    value = cell.BooleanCellValue;
                    valueType = BooleanType;
                    break;

                case CellType.Numeric:
                    if (TryGetTemporalValue(cell, out var temporalValue, out var temporalType))
                    {
                        value = temporalValue;
                        valueType = temporalType;
                    }
                    else
                    {
                        value = cell.NumericCellValue;
                        valueType = DoubleType;
                    }
                    break;

                case CellType.Error:
                    value = GetErrorValue(cell);
                    valueType = StringType;
                    break;

                default: // CellType.Unknown, CellType.Blank, CellType.Formula
                    value = null;
                    valueType = null;
                    break;
            }

            return new CellValueResult(valueType, value, cachedResultType);
        }

        /// <summary>
        /// 尝试获取时间相关的值，优化版本
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="value">输出值</param>
        /// <param name="valueType">输出类型</param>
        /// <returns>是否为时间类型</returns>
        private static bool TryGetTemporalValue(ICell cell, out object? value, out Type? valueType)
        {
            if (NpoiTemporalHelper.TryGetTemporal(cell, out var result))
            {
                switch (result.Kind)
                {
                    case TemporalKind.DateOnly:
                        value = result.DateOnly;
                        valueType = DateOnlyType;
                        return true;

                    case TemporalKind.TimeOnly:
                        value = result.TimeOnly;
                        valueType = TimeOnlyType;
                        return true;

                    case TemporalKind.DateTime:
                        value = result.DateTime;
                        valueType = DateTimeType;
                        return true;
                }
            }

            value = null;
            valueType = null;
            return false;
        }

        /// <summary>
        /// 获取工作簿的日期系统信息（带缓存）
        /// </summary>
        /// <param name="workbook">工作簿</param>
        /// <returns>是否使用1904日期系统</returns>
        public static void Is1904DateSystem(IWorkbook workbook)
        {

            if (workbook == null)
            {
                return;
            }

            try
            {
                _workbookCacheInfo = new WorkbookCacheInfo(workbook.IsDate1904());
            }
            catch
            {
                // 保持默认值，记录日志或处理异常的地方
            }
        }


        #region 根据指定类型获取单元格值并转换
        /// <summary>
        /// 根据指定类型获取单元格值并转换
        /// </summary>
        public static T NPOIGetValueBySpecifyType<T>(ICell cell)
        {
            var targetType = typeof(T);

            if (cell == null)
                return HandleNullValue<T>(targetType);

            var result = NPOIGetValueByCellType(cell);
            if (result.Value == null)
                return HandleNullValue<T>(targetType);

            try
            {
                return ConvertValue<T>(result.Value, result.ValueType!, targetType);
            }
            catch(Exception ex)
            {
                throw new InvalidOperationException(
                    $"无法将单元格值 '{result.Value}' ({result.ValueType?.Name}) 转换为 {targetType.Name}。", ex);
            }
        }

        /// <summary>
        /// 判断类型是否可以为 null
        /// </summary>
        public static bool CanBeNull(Type type)
        {
            if (!type.IsValueType)
                return true; // 引用类型可以为 null

            // 可空值类型，比如 int?
            if (Nullable.GetUnderlyingType(type) != null)
                return true;

            return false; // 普通值类型
        }

        private static T HandleNullValue<T>(Type targetType)
        {
            if (CanBeNull(targetType))
                return default!;

            throw new InvalidOperationException($"单元格值为空，无法转换为非空类型 {targetType.Name}。");
        }

        private static T ConvertDateTimeTypes<T>(object value, Type targetType, DateTimeConversionOptions? options = null)
        {
            options ??= new DateTimeConversionOptions();

            return HandleNullableType<T>(value, targetType, (v, t) => ConvertDateTimeValue(v, t, options));
        }

        /// <summary>
        /// 日期时间值转换
        /// </summary>
        private static object ConvertDateTimeValue(object value, Type targetType, DateTimeConversionOptions options)
        {
            return value switch
            {
                DateTime dt when targetType == typeof(DateOnly) =>
                    DateOnly.FromDateTime(dt),

                DateTime dt when targetType == typeof(TimeOnly) =>
                    TimeOnly.FromDateTime(dt),

                DateOnly dateOnly when targetType == typeof(DateTime) =>
                    dateOnly.ToDateTime(options.DefaultTime),

                TimeOnly timeOnly when targetType == typeof(DateTime) =>
                    options.DefaultDate.ToDateTime(timeOnly),

                DateOnly when targetType == typeof(TimeOnly) =>
                    throw new InvalidOperationException("DateOnly 不包含时间信息，无法转换为 TimeOnly。"),

                TimeOnly when targetType == typeof(DateOnly) =>
                    throw new InvalidOperationException("TimeOnly 不包含日期信息，无法转换为 DateOnly。"),

                _ => throw new InvalidOperationException($"不支持从 {value.GetType().Name} 转换到 {targetType.Name}。")
            };
        }

        private static T ConvertValue<T>(object value, Type sourceType, Type targetType)
        {
            // 直接类型匹配
            if (sourceType == targetType)
                return (T)value;

            // 字符串转换优先处理
            if (targetType == typeof(string))
            {
                return (T)(object)ConvertToString(value);
            }

            // 从字符串转换
            if (sourceType == typeof(string))
            {
                return ConvertFromString<T>((string)value, targetType);
            }

            // 布尔类型转换（包括nullable）
            if (IsBooleanType(targetType))
            {
                return ConvertBooleanTypes<T>(value, targetType);
            }

            // 日期时间类型双向转换
            if (IsDateTimeType(sourceType) || IsDateTimeType(targetType))
            {
                return ConvertDateTimeTypes<T>(value, targetType);
            }

            // 数值类型安全转换
            if (IsNumericType(targetType) && IsNumericType(sourceType))
                return ConvertNumericSafely<T>(value, targetType);

            // 默认转换
            return (T)Convert.ChangeType(value, targetType);
        }

        public static bool IsNumericType(Type type)
        {
            Type t = Nullable.GetUnderlyingType(type) ?? type;
            switch (Type.GetTypeCode(t))
            {
                case TypeCode.Byte:
                case TypeCode.SByte:
                case TypeCode.UInt16:
                case TypeCode.UInt32:
                case TypeCode.UInt64:
                case TypeCode.Int16:
                case TypeCode.Int32:
                case TypeCode.Int64:
                case TypeCode.Decimal:
                case TypeCode.Double:
                case TypeCode.Single:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 检查类型是否为日期时间类型
        /// </summary>
        private static bool IsDateTimeType(Type type)
        {
            return DateTimeTypes.Contains(Nullable.GetUnderlyingType(type) ?? type);
        }

        /// <summary>
        /// 统一的nullable类型处理
        /// </summary>
        private static T HandleNullableType<T>(object value, Type targetType, Func<object, Type, object> converter)
        {
            var underlyingType = Nullable.GetUnderlyingType(targetType);
            if (underlyingType != null)
            {
                var result = converter(value, underlyingType);
                return (T)result;
            }

            return (T)converter(value, targetType);
        }

        /// <summary>
        /// 检查类型是否为布尔类型
        /// </summary>
        private static bool IsBooleanType(Type type)
        {
            return BooleanTypes.Contains(Nullable.GetUnderlyingType(type) ?? type);
        }

        /// <summary>
        /// 布尔类型转换
        /// </summary>
        private static T ConvertBooleanTypes<T>(object value, Type targetType)
        {
            return HandleNullableType<T>(value, targetType, (v, t) => ConvertBooleanValue(v));
        }

        /// <summary>
        /// 转换为布尔值
        /// </summary>
        private static object ConvertBooleanValue(object value)
        {
            return value switch
            {
                bool b => b,
                string s => ParseBoolean(s),
                int i => i != 0,
                double d => d != 0.0,
                _ => throw new InvalidOperationException($"无法将 {value.GetType().Name} 转换为布尔值。")
            };
        }

        /// <summary>
        /// 将对象转换为字符串，提供特殊类型的格式化
        /// </summary>
        private static string ConvertToString(object value)
        {
            return value switch
            {
                DateTime dt => dt.ToString("yyyy-MM-dd HH:mm:ss"),
                DateOnly date => date.ToString("yyyy-MM-dd"),
                TimeOnly time => time.ToString("HH:mm:ss"),
                double d when double.IsNaN(d) => "NaN",
                double d when double.IsPositiveInfinity(d) => "+∞",
                double d when double.IsNegativeInfinity(d) => "-∞",
                _ => value.ToString() ?? string.Empty
            };
        }

        /// <summary>
        /// 从字符串转换为指定类型
        /// </summary>
        private static T ConvertFromString<T>(string value, Type targetType)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                if (CanBeNull(targetType))
                    return default!;
                throw new InvalidOperationException($"空字符串无法转换为非空类型 {targetType.Name}。");
            }

            return targetType switch
            {
                var t when t == typeof(DateTime) => (T)(object)DateTime.Parse(value),
                var t when t == typeof(DateOnly) => (T)(object)DateOnly.Parse(value),
                var t when t == typeof(TimeOnly) => (T)(object)TimeOnly.Parse(value),
                var t when IsNumericType(t) => ConvertNumericSafely<T>(value, targetType),
                var t when t == typeof(bool) => (T)(object)ParseBoolean(value),
                _ => (T)Convert.ChangeType(value, targetType)
            };
        }

        /// <summary>
        /// 解析布尔值，支持多种格式
        /// </summary>
        private static bool ParseBoolean(string value)
        {
            return value.ToLowerInvariant() switch
            {
                "true" or "1" or "yes" or "y" or "是" => true,
                "false" or "0" or "no" or "n" or "否" => false,
                _ => throw new FormatException($"无法将 '{value}' 转换为布尔值。")
            };
        }

        private static T ConvertNumericSafely<T>(object value, Type targetType)
        {
            try
            {
                var doubleValue = Convert.ToDouble(value);

                return Type.GetTypeCode(targetType) switch
                {
                    // 有符号整数类型
                    TypeCode.SByte => ValidateAndConvert<T>(doubleValue, sbyte.MinValue, sbyte.MaxValue, v => (sbyte)v),
                    TypeCode.Int16 => ValidateAndConvert<T>(doubleValue, short.MinValue, short.MaxValue, v => (short)v),
                    TypeCode.Int32 => ValidateAndConvert<T>(doubleValue, int.MinValue, int.MaxValue, v => (int)v),
                    TypeCode.Int64 => ValidateAndConvert<T>(doubleValue, long.MinValue, long.MaxValue, v => (long)v),

                    // 无符号整数类型
                    TypeCode.Byte => ValidateAndConvert<T>(doubleValue, byte.MinValue, byte.MaxValue, v => (byte)v),
                    TypeCode.UInt16 => ValidateAndConvert<T>(doubleValue, ushort.MinValue, ushort.MaxValue, v => (ushort)v),
                    TypeCode.UInt32 => ValidateAndConvert<T>(doubleValue, uint.MinValue, uint.MaxValue, v => (uint)v),
                    TypeCode.UInt64 => ValidateAndConvert<T>(doubleValue, ulong.MinValue, ulong.MaxValue, v => (ulong)v),

                    // 浮点数类型
                    TypeCode.Single => ValidateFloatRange<T>(doubleValue, v => (float)v),
                    TypeCode.Double => (T)(object)doubleValue,
                    TypeCode.Decimal => ValidateDecimalRange<T>(doubleValue),

                    _ => HandleNonPrimitiveNumericTypes<T>(value, targetType)
                };
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"数值转换失败: 无法将 {value} 转换为 {targetType.Name}。{ex.Message}", ex);
            }
        }

        /// <summary>
        /// 整数类型验证和转换
        /// </summary>
        private static T ValidateAndConvert<T>(double value, double min, double max, Func<double, object> converter)
        {
            // 检查是否为整数
            if (value != Math.Truncate(value))
            {
                value = Math.Round(value, MidpointRounding.AwayFromZero);
            }

            // 检查范围
            if (value > max || value < min)
            {
                throw new OverflowException($"数值 {value} 超出目标类型范围 [{min}, {max}]。");
            }

            return (T)converter(value);
        }

        /// <summary>
        /// 浮点数类型验证
        /// </summary>
        private static T ValidateFloatRange<T>(double value, Func<double, object> converter)
        {
            if (double.IsInfinity(value) || double.IsNaN(value))
            {
                throw new InvalidOperationException($"无法转换特殊浮点值: {value}");
            }

            if (Math.Abs(value) > float.MaxValue)
            {
                throw new OverflowException($"数值 {value} 超出 float 类型范围。");
            }

            return (T)converter(value);
        }

        /// <summary>
        /// Decimal 类型特殊处理
        /// </summary>
        private static T ValidateDecimalRange<T>(double value)
        {
            if (value > (double)decimal.MaxValue || value < (double)decimal.MinValue)
            {
                throw new OverflowException($"数值 {value} 超出 decimal 类型范围。");
            }

            try
            {
                return (T)(object)(decimal)value;
            }
            catch (OverflowException)
            {
                throw new OverflowException($"数值 {value} 无法精确转换为 decimal 类型。");
            }
        }

        /// <summary>
        /// 处理非基元数值类型（如可空类型）
        /// </summary>
        private static T HandleNonPrimitiveNumericTypes<T>(object value, Type targetType)
        {
            return HandleNullableType<T>(value, targetType, (v, t) => ConvertNumericSafely<object>(v, t));
        }
        #endregion
    }
}
