﻿using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Org.BouncyCastle.Asn1.X509.Qualified;

namespace TempConsoleApp1
{
    /// <summary>
    /// 单元格值结果
    /// </summary>
    /// <param name="ValueType">实际值的 C# 类型</param>
    /// <param name="Value">单元格值</param>
    /// <param name="OriginalCellType">NPOI 原始单元格类型</param>
    public sealed record CellValueResult(Type? ValueType, object? Value, CellType OriginalCellType);

    /// <summary>
    /// 工作簿缓存信息
    /// </summary>
    /// <param name="Is1904DateSystem">是否使用1904日期系统</param>
    public sealed record WorkbookCacheInfo(bool Is1904DateSystem);

    public static class ExcelHelper
    {
        // 私有静态字段，通过属性访问以提供更好的封装
        private static WorkbookCacheInfo _workbookCacheInfo = new(false);

        /// <summary>
        /// 获取当前工作簿缓存信息
        /// </summary>
        public static WorkbookCacheInfo WorkbookCacheInfo => _workbookCacheInfo;

        // 常用类型的直接引用（保留用于非映射场景）
        private static readonly Type StringType = typeof(string);
        private static readonly Type BooleanType = typeof(bool);
        private static readonly Type IntType = typeof(int);
        private static readonly Type DoubleType = typeof(double);
        private static readonly Type DateTimeType = typeof(DateTime);
        private static readonly Type DateOnlyType = typeof(DateOnly);
        private static readonly Type TimeOnlyType = typeof(TimeOnly);
        /// <summary>
        /// NPOI通过单元格类型获取单元格值
        /// </summary>
        /// <param name="cell">NPOI单元格对象</param>
        /// <param name="getFormulaValue">是否获取公式计算结果值，false则返回公式字符串</param>
        /// <returns>包含实际值类型、值和原始单元格类型的结果对象</returns>
        public static CellValueResult NPOIGetValueByCellType(ICell cell, bool getFormulaValue = true)
        {
            if (cell == null)
            {
                return new CellValueResult(null, null, CellType.Blank);
            }

            var originalCellType = cell.CellType;

            // 优化：直接处理最常见的类型，避免 switch 表达式的开销
            object? value;
            Type? valueType;

            switch (originalCellType)
            {
                case CellType.String:
                    value = cell.StringCellValue;
                    valueType = StringType;
                    break;

                case CellType.Boolean:
                    value = cell.BooleanCellValue;
                    valueType = BooleanType;
                    break;

                case CellType.Numeric:
                    if (TryGetTemporalValue(cell, out var temporalValue, out var temporalType))
                    {
                        value = temporalValue;
                        valueType = temporalType;
                    }
                    else
                    {
                        value = cell.NumericCellValue;
                        valueType = DoubleType;
                    }
                    break;

                case CellType.Error:
                    value = GetErrorValue(cell);
                    valueType = StringType;
                    break;

                case CellType.Formula:
                    if (getFormulaValue)
                    {
                        var formulaResult = GetValueByCellFormulaResultType(cell);
                        value = formulaResult.Value;
                        valueType = formulaResult.ValueType;
                    }
                    else
                    {
                        value = cell.CellFormula;
                        valueType = StringType;
                    }
                    break;

                default: // CellType.Unknown, CellType.Blank
                    value = null;
                    valueType = null;
                    break;
            }

            return new CellValueResult(valueType, value, originalCellType);
        }

        public static T NPOIGetValueBySpecifyType<T>(ICell cell, Type specifyType)
        {
            if (specifyType == null)
            {
                throw new ArgumentNullException(nameof(specifyType));
            }

            if (cell == null)
            {
                return default!;
            }

            object? value;
            try
            {
                value = specifyType switch
                {
                    var _ when specifyType == StringType => cell.StringCellValue,
                    var _ when specifyType == BooleanType => cell.BooleanCellValue,
                    var _ when specifyType == IntType => (int)cell.NumericCellValue,
                    var _ when specifyType == DoubleType => cell.NumericCellValue,
                    var _ when specifyType == DateTimeType => cell.DateCellValue,
                    var _ when specifyType == DateOnlyType => cell.DateOnlyCellValue,
                    var _ when specifyType == TimeOnlyType => cell.TimeOnlyCellValue,
                    _ => null,
                };
            }
            catch
            {
                throw;
            }

            return value;
        }

        /// <summary>
        /// 获取错误单元格的错误信息
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>错误信息字符串</returns>
        private static string GetErrorValue(ICell cell)
        {
            if (cell is XSSFCell xSSFCell)
            {
                return xSSFCell.ErrorCellString;
            }
            else
            {
                byte errorCode = cell.ErrorCellValue;
                return FormulaError.ForInt(errorCode).String;
            }
        }

        /// <summary>
        /// NPOI公式单元格获取值
        /// </summary>
        /// <param name="cell">单元格对象</param>
        /// <returns>包含实际值类型、值和原始单元格类型的结果对象</returns>
        private static CellValueResult GetValueByCellFormulaResultType(ICell cell)
        {
            if (cell == null)
            {
                return new CellValueResult(null, null, CellType.Blank);
            }

            var cachedResultType = cell.CachedFormulaResultType;
            object? value;
            Type? valueType;

            switch (cachedResultType)
            {
                case CellType.String:
                    value = cell.StringCellValue;
                    valueType = StringType;
                    break;

                case CellType.Boolean:
                    value = cell.BooleanCellValue;
                    valueType = BooleanType;
                    break;

                case CellType.Numeric:
                    if (TryGetTemporalValue(cell, out var temporalValue, out var temporalType))
                    {
                        value = temporalValue;
                        valueType = temporalType;
                    }
                    else
                    {
                        value = cell.NumericCellValue;
                        valueType = DoubleType;
                    }
                    break;

                case CellType.Error:
                    value = GetErrorValue(cell);
                    valueType = StringType;
                    break;

                default: // CellType.Unknown, CellType.Blank, CellType.Formula
                    value = null;
                    valueType = null;
                    break;
            }

            return new CellValueResult(valueType, value, cachedResultType);
        }

        /// <summary>
        /// 尝试获取时间相关的值，优化版本
        /// </summary>
        /// <param name="cell">单元格</param>
        /// <param name="value">输出值</param>
        /// <param name="valueType">输出类型</param>
        /// <returns>是否为时间类型</returns>
        private static bool TryGetTemporalValue(ICell cell, out object? value, out Type? valueType)
        {
            if (NpoiTemporalHelper.TryGetTemporal(cell, out var result))
            {
                switch (result.Kind)
                {
                    case TemporalKind.DateOnly:
                        value = result.DateOnly;
                        valueType = DateOnlyType;
                        return true;

                    case TemporalKind.TimeOnly:
                        value = result.TimeOnly;
                        valueType = TimeOnlyType;
                        return true;

                    case TemporalKind.DateTime:
                        value = result.DateTime;
                        valueType = DateTimeType;
                        return true;
                }
            }

            value = null;
            valueType = null;
            return false;
        }

        /// <summary>
        /// 获取工作簿的日期系统信息（带缓存）
        /// </summary>
        /// <param name="workbook">工作簿</param>
        /// <returns>是否使用1904日期系统</returns>
        public static void Is1904DateSystem(IWorkbook workbook)
        {

            if (workbook == null)
            {
                return;
            }

            try
            {
                _workbookCacheInfo = new WorkbookCacheInfo(workbook.IsDate1904());
            }
            catch
            {
                // 保持默认值，记录日志或处理异常的地方
            }
        }
    }
}
