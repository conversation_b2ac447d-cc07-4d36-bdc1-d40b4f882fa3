﻿using NPOI.SS.UserModel;

namespace TempConsoleApp1
{
    internal class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Excel 文件读取测试 ===");
            Console.WriteLine();

            // 读取 Excel 文件
            string filePath = "npoi读取.xlsx";
            if (File.Exists(filePath))
            {
                ReadExcelFile(filePath);
            }
            else
            {
                Console.WriteLine($"文件 {filePath} 不存在。");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取 Excel 文件并显示单元格类型信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private static void ReadExcelFile(string filePath)
        {
            FileStream? fileStream = null;
            IWorkbook? workbook = null;

            try
            {
                Console.WriteLine($"读取文件: {filePath}");
                fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                workbook = new NPOI.XSSF.UserModel.XSSFWorkbook(fileStream);
                ISheet sheet = workbook.GetSheetAt(0);

                // 显示工作簿信息（使用缓存）
                ExcelHelper.Is1904DateSystem(workbook);
                Console.WriteLine();

                // 读取数据
                for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
                {
                    var row = sheet.GetRow(rowIndex);
                    if (row == null) continue;

                    Console.WriteLine($"第 {rowIndex + 1} 行:");
                    for (int colIndex = 0; colIndex < row.LastCellNum; colIndex++)
                    {
                        var cell = row.GetCell(colIndex);
                        var result = ExcelHelper.NPOIGetValueByCellType(cell);

                        Console.WriteLine($"  列 {colIndex + 1}: 类型={result.ValueType?.Name ?? "null"}, 值={result.Value}, NPOI类型={result.OriginalCellType}");
                    }
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            finally
            {
                workbook?.Close();
                workbook?.Dispose();
                fileStream?.Dispose();
            }
        }
    }
}
